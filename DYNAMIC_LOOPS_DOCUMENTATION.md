# Dynamic Looping Structures for JSON to YAML Conversion

## Overview

The enhanced JSON to YAML conversion system now automatically detects arrays of objects in JSON input and generates appropriate Moveworks-compatible "for" loop steps. This enables seamless processing of sequential data without requiring manual YAML structure modification.

## Key Features

### 1. Automatic Array Detection
- **Smart Detection**: Automatically identifies arrays containing objects (not just primitive values)
- **Pattern Recognition**: Recognizes common array naming patterns (products, users, items, etc.)
- **Flexible Handling**: Works with arrays of any size (1 item to 100+ items)

### 2. Dynamic Variable Naming
- **Intelligent Naming**: Generates meaningful variable names based on array content
- **Singularization**: Converts plural array names to singular item variables
- **Consistent Patterns**: Follows Moveworks naming conventions

### 3. Context-Aware Processing
- **Item Type Detection**: Analyzes item structure to determine appropriate processing
- **Specialized Actions**: Creates different action types based on item properties:
  - **ID-based items**: Items with `id` or `_id` fields
  - **User items**: Items with email fields → uses `mw.get_user_by_email`
  - **API items**: Items with URL/endpoint fields → prepares API calls
  - **Generic items**: Fallback for any other structure

### 4. Progress Updates
- **User Experience**: Includes progress updates for better user feedback
- **Contextual Messages**: Tailored messages based on processing type
- **Completion Tracking**: Clear indication of processing status

## Generated YAML Structure

### Basic For Loop Format
```yaml
steps:
  - for:
      each: {item_variable}        # e.g., "product", "user", "item"
      index: {item_variable}_index # e.g., "product_index"
      in: data.{array_name}        # e.g., "data.products"
      output_key: {array_name}_processing_results
      steps:
        - {processing_action}      # Appropriate action based on item type
```

### Multiple Arrays Handling
When multiple arrays are detected:
1. **Initial Setup**: Non-array data is prepared first
2. **Sequential Processing**: Each array gets its own for loop
3. **Final Aggregation**: Results are combined in a final step

## Examples

### Example 1: Products Array
**Input JSON:**
```json
{
  "store_name": "Tech Store",
  "products": [
    {"id": "prod_001", "name": "Laptop", "price": 999.99},
    {"id": "prod_002", "name": "Mouse", "price": 29.99}
  ]
}
```

**Generated YAML:**
```yaml
steps:
  - script:
      output_key: initial_data_setup
      input_args:
        store_name: Tech Store
      code: |
        result = {"store_name": store_name}
        result
  - for:
      each: product
      index: product_index
      in: data.products
      output_key: products_processing_results
      steps:
        - script:
            output_key: processed_item_data
            input_args:
              item_data: product
            code: |
              # Process item with ID
              item_id = item_data.get("id", "unknown")
              processed_result = {
                "item_id": item_id,
                "processing_status": "completed",
                "processed_fields": {
                  "name": item_data.get("name", None),
                  "price": item_data.get("price", None)
                },
                "timestamp": "processed"
              }
              processed_result
```

### Example 2: Users Array
**Input JSON:**
```json
{
  "department": "Engineering",
  "users": [
    {"email": "<EMAIL>", "name": "John Doe"},
    {"email": "<EMAIL>", "name": "Jane Smith"}
  ]
}
```

**Generated YAML:**
```yaml
steps:
  - for:
      each: user
      index: user_index
      in: data.users
      output_key: users_processing_results
      steps:
        - action:
            action_name: mw.get_user_by_email
            output_key: user_lookup_result
            input_args:
              user_email: user.email
            progress_updates:
              on_pending: Looking up user information...
              on_complete: User lookup completed
```

## Variable Naming Patterns

### Array Name → Item Variable Mapping
| Array Name | Item Variable | Index Variable | Output Key |
|------------|---------------|----------------|------------|
| products | product | product_index | products_processing_results |
| users | user | user_index | users_processing_results |
| items | item | item_index | items_processing_results |
| orders | order | order_index | orders_processing_results |
| tasks | task | task_index | tasks_processing_results |
| employees | employee | employee_index | employees_processing_results |

### Fallback Rules
- If array name ends with 's': remove 's' (e.g., "records" → "record")
- If no pattern match: use "item" as default

## Processing Logic Types

### 1. ID-Based Processing
**Triggers**: Items with `id` or `_id` fields
**Action**: Script that extracts ID and processes all fields
**Use Case**: Products, records, documents with unique identifiers

### 2. User Processing  
**Triggers**: Items with `email` or `user_email` fields
**Action**: `mw.get_user_by_email` Moveworks action
**Use Case**: Employee lists, user directories, contact information

### 3. API Processing
**Triggers**: Items with `url` or `endpoint` fields  
**Action**: Script that prepares API request data
**Use Case**: Service endpoints, webhook configurations, API integrations

### 4. Generic Processing
**Triggers**: Any other item structure
**Action**: Script that extracts and validates all fields
**Use Case**: Flexible handling of any object structure

## Benefits

### 1. Scalability
- **No Size Limits**: Handles arrays from 1 to 100+ items automatically
- **Memory Efficient**: Processes items iteratively, not all at once
- **Performance Optimized**: Uses appropriate Moveworks constructs

### 2. Maintainability  
- **Consistent Structure**: All generated loops follow same pattern
- **Clear Naming**: Variables and keys are self-documenting
- **Modular Design**: Each array processed independently

### 3. User Experience
- **Progress Feedback**: Users see processing status
- **Error Handling**: Built-in error management in generated code
- **Predictable Output**: Consistent result structure

### 4. Moveworks Compliance
- **Proper Syntax**: Follows Moveworks YAML specifications
- **Built-in Actions**: Uses `mw.` prefix for platform actions
- **Data Access**: Correct `data.` prefix for variable access
- **Progress Updates**: Includes user experience enhancements

## Integration

The dynamic looping functionality is automatically enabled in the JSON to YAML conversion process. No additional configuration is required - simply provide JSON with arrays of objects and the system will generate appropriate for loop structures.

## Future Enhancements

- **Nested Array Support**: Handle arrays within array items
- **Conditional Processing**: Add switch statements within loops
- **Parallel Processing**: Option to use `parallel` for independent items
- **Custom Processing**: User-defined processing templates
